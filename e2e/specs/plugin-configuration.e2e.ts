import {
  getSharedTestContext,
  resetSharedTestContext,
  cleanupTestState,
  type SharedTestContext
} from '../helpers/shared-context';
import { test, beforeAll, beforeEach, afterEach, expect } from 'vitest';

describe("Plugin Configuration", () => {
  let context: SharedTestContext;

  beforeAll(async () => { context = await getSharedTestContext(); });

  beforeEach(async () => { await resetSharedTestContext(); });

  afterEach(async () => { await cleanupTestState(); });

  test("should open plugin settings and configure basic options", async () => {
    // Open the settings using the correct command
    await context.page.keyboard.press('Meta+Comma'); // Standard Obsidian settings shortcut

    // Wait for settings modal to appear
    await context.page.waitForSelector('.modal-container', { timeout: 10000 });

    // Navigate to Community plugins section
    await context.page.click('text=Community plugins');
    await context.page.waitForTimeout(1000);

    // Find the Ghost Sync plugin in the list
    await context.page.waitForSelector('text=Ghost Sync', { timeout: 5000 });

    // Look for the settings gear icon next to Ghost Sync
    const settingsButton = context.page.locator('.setting-item').filter({ hasText: 'Ghost Sync' }).locator('.clickable-icon[aria-label="Settings"]');
    await settingsButton.click();

    // Wait for plugin settings to load
    await context.page.waitForSelector('text=Ghost Sync Settings', { timeout: 5000 });

    // Test Ghost URL configuration
    const ghostUrlInput = context.page.locator('input[placeholder="https://your-site.ghost.io"]');
    await ghostUrlInput.clear();
    await ghostUrlInput.fill('https://test-ghost-site.com');

    // Test Ghost Admin API Key configuration
    const apiKeyInput = context.page.locator('input[placeholder="id:secret"]');
    await apiKeyInput.clear();
    await apiKeyInput.fill('test123:secret456');

    // Test Articles directory configuration
    const articlesDirInput = context.page.locator('input[placeholder="articles"]');
    await articlesDirInput.clear();
    await articlesDirInput.fill('my-articles');

    // Test verbose toggle
    const verboseToggle = context.page.locator('.checkbox-container').filter({ hasText: 'Verbose output' }).locator('input[type="checkbox"]');
    await verboseToggle.check();

    // Close settings
    await context.page.keyboard.press('Escape');

    // Verify that the plugin settings were updated
    const pluginSettings = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin ? plugin.settings : null;
    });

    expect(pluginSettings).toBeTruthy();
    expect(pluginSettings.ghostUrl).toBe('https://test-ghost-site.com');
    expect(pluginSettings.ghostAdminApiKey).toBe('test123:secret456');
    expect(pluginSettings.articlesDir).toBe('my-articles');
    expect(pluginSettings.verbose).toBe(true);
  });

  test("should persist plugin settings after restart", async () => {
    // Configure plugin settings
    await context.page.keyboard.press('Meta+Comma');
    await context.page.waitForSelector('.modal-container', { timeout: 10000 });
    await context.page.click('text=Community plugins');
    await context.page.waitForTimeout(1000);

    const settingsButton = context.page.locator('.setting-item').filter({ hasText: 'Ghost Sync' }).locator('.clickable-icon[aria-label="Settings"]');
    await settingsButton.click();

    await context.page.waitForSelector('text=Ghost Sync Settings', { timeout: 5000 });

    // Set specific test values
    const testUrl = 'https://persistent-test.ghost.io';
    const testApiKey = 'persist123:secret789';
    const testDir = 'persistent-articles';

    const ghostUrlInput = context.page.locator('input[placeholder="https://your-site.ghost.io"]');
    await ghostUrlInput.clear();
    await ghostUrlInput.fill(testUrl);

    const apiKeyInput = context.page.locator('input[placeholder="id:secret"]');
    await apiKeyInput.clear();
    await apiKeyInput.fill(testApiKey);

    const articlesDirInput = context.page.locator('input[placeholder="articles"]');
    await articlesDirInput.clear();
    await articlesDirInput.fill(testDir);

    // Close settings to save
    await context.page.keyboard.press('Escape');

    // Simulate plugin reload by disabling and re-enabling
    await context.page.evaluate(async () => {
      const app = (window as any).app;
      await app.plugins.disablePlugin('ghost-sync');
      await app.plugins.enablePlugin('ghost-sync');
    });

    // Wait for plugin to reload
    await context.page.waitForTimeout(1000);

    // Verify settings persisted
    const persistedSettings = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin ? plugin.settings : null;
    });

    expect(persistedSettings).toBeTruthy();
    expect(persistedSettings.ghostUrl).toBe(testUrl);
    expect(persistedSettings.ghostAdminApiKey).toBe(testApiKey);
    expect(persistedSettings.articlesDir).toBe(testDir);
  });

  test("should validate Ghost API key format", async () => {
    await context.page.keyboard.press('Meta+Comma');
    await context.page.waitForSelector('.modal-container', { timeout: 10000 });
    await context.page.click('text=Community plugins');
    await context.page.waitForTimeout(1000);

    const settingsButton = context.page.locator('.setting-item').filter({ hasText: 'Ghost Sync' }).locator('.clickable-icon[aria-label="Settings"]');
    await settingsButton.click();

    await context.page.waitForSelector('text=Ghost Sync Settings', { timeout: 5000 });

    // Test invalid API key format
    const apiKeyInput = context.page.locator('input[placeholder="id:secret"]');
    await apiKeyInput.clear();
    await apiKeyInput.fill('invalid-key-format');

    // The plugin should still accept the input (validation might be done elsewhere)
    const currentValue = await apiKeyInput.inputValue();
    expect(currentValue).toBe('invalid-key-format');

    // Test valid API key format
    await apiKeyInput.clear();
    await apiKeyInput.fill('1234567890abcdef1234567890:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef');

    const validValue = await apiKeyInput.inputValue();
    expect(validValue).toBe('1234567890abcdef1234567890:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef');
  });

  test("should update articles directory and affect plugin behavior", async () => {
    // Configure a custom articles directory
    await context.page.keyboard.press('Meta+Comma');
    await context.page.waitForSelector('.modal-container', { timeout: 10000 });
    await context.page.click('text=Community plugins');
    await context.page.waitForTimeout(1000);

    const settingsButton = context.page.locator('.setting-item').filter({ hasText: 'Ghost Sync' }).locator('.clickable-icon[aria-label="Settings"]');
    await settingsButton.click();

    await context.page.waitForSelector('text=Ghost Sync Settings', { timeout: 5000 });

    const customDir = 'custom-posts';
    const articlesDirInput = context.page.locator('input[placeholder="articles"]');
    await articlesDirInput.clear();
    await articlesDirInput.fill(customDir);

    await context.page.keyboard.press('Escape');

    // Verify the plugin's appAdapter was updated
    const appAdapterDir = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin && plugin.appAdapter ? plugin.appAdapter.articlesDir : null;
    });

    expect(appAdapterDir).toBe(customDir);
  });
});
